#!/usr/bin/env python3
"""
Production System Tests
======================

Comprehensive testing framework for the production trading system including:
- Unit tests for all components
- Integration tests for system workflows
- Performance tests for latency and throughput
- Stress tests for high-load scenarios
- Risk management validation tests
- Recovery system tests

Features:
- Automated test execution
- Performance benchmarking
- Risk scenario testing
- System health validation
- Recovery mechanism testing
- Production readiness assessment
"""

import asyncio
import pytest
import logging
import json
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.production_risk_manager import ProductionRiskManager
from agents.production_execution_agent import ProductionExecutionAgent
from agents.agent_health_monitor import AgentHealthMonitor
from utils.advanced_position_sizing import AdvancedPositionSizing
from utils.circuit_breaker_system import CircuitBreakerSystem
from utils.drawdown_recovery_system import DrawdownRecoverySystem
from utils.production_monitoring import ProductionMonitoring

logger = logging.getLogger(__name__)

class ProductionSystemTests:
    """
    Comprehensive production system test suite
    """
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        self.test_config = self._load_test_config()
        
    def _load_test_config(self) -> Dict[str, Any]:
        """Load test configuration"""
        return {
            'test_capital': 100000,
            'max_test_duration': 300,  # 5 minutes
            'performance_thresholds': {
                'max_latency_ms': 1000,
                'min_success_rate': 0.95,
                'max_memory_usage_mb': 500
            },
            'risk_test_scenarios': [
                {'drawdown': 0.05, 'expected_action': 'continue'},
                {'drawdown': 0.10, 'expected_action': 'reduce_risk'},
                {'drawdown': 0.15, 'expected_action': 'stop_trading'},
                {'drawdown': 0.20, 'expected_action': 'emergency_stop'}
            ]
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run comprehensive test suite"""
        try:
            logger.info("[TEST] Starting comprehensive production system tests")
            
            test_results = {
                'timestamp': datetime.now().isoformat(),
                'test_summary': {},
                'component_tests': {},
                'integration_tests': {},
                'performance_tests': {},
                'risk_tests': {},
                'recovery_tests': {},
                'overall_status': 'UNKNOWN'
            }
            
            # Component tests
            logger.info("[TEST] Running component tests...")
            test_results['component_tests'] = await self._run_component_tests()
            
            # Integration tests
            logger.info("[TEST] Running integration tests...")
            test_results['integration_tests'] = await self._run_integration_tests()
            
            # Performance tests
            logger.info("[TEST] Running performance tests...")
            test_results['performance_tests'] = await self._run_performance_tests()
            
            # Risk management tests
            logger.info("[TEST] Running risk management tests...")
            test_results['risk_tests'] = await self._run_risk_tests()
            
            # Recovery system tests
            logger.info("[TEST] Running recovery system tests...")
            test_results['recovery_tests'] = await self._run_recovery_tests()
            
            # Calculate overall status
            test_results['overall_status'] = self._calculate_overall_status(test_results)
            test_results['test_summary'] = self._generate_test_summary(test_results)
            
            logger.info(f"[TEST] Test suite completed - Status: {test_results['overall_status']}")
            
            return test_results
            
        except Exception as e:
            logger.error(f"[ERROR] Test suite failed: {e}")
            return {'error': str(e), 'overall_status': 'FAILED'}
    
    async def _run_component_tests(self) -> Dict[str, Any]:
        """Test individual components"""
        try:
            results = {}
            
            # Test Risk Manager
            results['risk_manager'] = await self._test_risk_manager()
            
            # Test Execution Agent
            results['execution_agent'] = await self._test_execution_agent()
            
            # Test Position Sizing
            results['position_sizing'] = await self._test_position_sizing()
            
            # Test Circuit Breakers
            results['circuit_breakers'] = await self._test_circuit_breakers()
            
            # Test Health Monitor
            results['health_monitor'] = await self._test_health_monitor()
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Component tests failed: {e}")
            return {'error': str(e)}
    
    async def _test_risk_manager(self) -> Dict[str, Any]:
        """Test production risk manager"""
        try:
            # Mock configuration
            mock_config = Mock()
            mock_config.initial_balance = self.test_config['test_capital']
            
            # Mock event bus
            mock_event_bus = AsyncMock()
            
            # Create risk manager
            risk_manager = ProductionRiskManager(mock_event_bus, mock_config, "test_session")
            
            # Test initialization
            assert risk_manager.total_capital == self.test_config['test_capital']
            assert risk_manager.max_portfolio_risk == 0.02
            
            # Test trade validation
            mock_trade_request = Mock()
            mock_trade_request.symbol = "NIFTY"
            mock_trade_request.quantity = 100
            mock_trade_request.entry_price = 18000
            mock_trade_request.stop_loss = 17900
            mock_trade_request.take_profit = 18200
            mock_trade_request.capital_allocated = 10000
            mock_trade_request.signal_id = "test_signal_001"
            mock_trade_request.strategy_name = "test_strategy"
            
            # Test position sizing
            position_size, risk_amount = await risk_manager._calculate_optimal_position_size(mock_trade_request)
            
            assert position_size > 0, "Position size should be positive"
            assert risk_amount > 0, "Risk amount should be positive"
            assert risk_amount <= risk_manager.total_capital * risk_manager.max_position_risk, "Risk should not exceed limits"
            
            # Test risk-reward calculation
            rr_ratio = await risk_manager._calculate_risk_reward_ratio(mock_trade_request)
            assert rr_ratio > 0, "Risk-reward ratio should be positive"
            
            return {
                'status': 'PASSED',
                'tests_run': 4,
                'tests_passed': 4,
                'details': {
                    'initialization': 'PASSED',
                    'position_sizing': 'PASSED',
                    'risk_calculation': 'PASSED',
                    'risk_reward_ratio': 'PASSED'
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Risk manager test failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'tests_run': 4,
                'tests_passed': 0
            }
    
    async def _test_execution_agent(self) -> Dict[str, Any]:
        """Test production execution agent"""
        try:
            # Mock configuration
            mock_config = Mock()
            mock_config.paper_trading_enabled = True
            
            # Mock event bus
            mock_event_bus = AsyncMock()
            
            # Create execution agent
            execution_agent = ProductionExecutionAgent(mock_event_bus, mock_config, "test_session")
            
            # Test initialization
            assert execution_agent.execution_state.value == "normal"
            assert execution_agent.is_trading_enabled == True
            
            # Test order creation
            mock_trade_request = Mock()
            mock_trade_request.symbol = "NIFTY"
            mock_trade_request.quantity = 100
            mock_trade_request.entry_price = 18000
            mock_trade_request.direction = Mock()
            mock_trade_request.direction.value = "BUY"
            mock_trade_request.order_type = Mock()
            mock_trade_request.order_type.value = "MARKET"
            
            execution_order = execution_agent._create_execution_order(mock_trade_request)
            
            assert execution_order.symbol == "NIFTY"
            assert execution_order.quantity == 100
            assert execution_order.price == 18000
            assert execution_order.side == "BUY"
            
            # Test paper execution
            start_time = time.time()
            success, message = await execution_agent._execute_paper_order(execution_order)
            execution_time = (time.time() - start_time) * 1000
            
            assert success == True, "Paper execution should succeed"
            assert execution_time < self.test_config['performance_thresholds']['max_latency_ms'], "Execution should be fast"
            
            return {
                'status': 'PASSED',
                'tests_run': 3,
                'tests_passed': 3,
                'execution_time_ms': execution_time,
                'details': {
                    'initialization': 'PASSED',
                    'order_creation': 'PASSED',
                    'paper_execution': 'PASSED'
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Execution agent test failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'tests_run': 3,
                'tests_passed': 0
            }
    
    async def _test_position_sizing(self) -> Dict[str, Any]:
        """Test advanced position sizing"""
        try:
            # Create position sizing system
            config = {
                'total_capital': self.test_config['test_capital'],
                'max_position_risk': 0.02,
                'min_position_size': 0.001,
                'max_position_size': 0.05
            }
            
            position_sizing = AdvancedPositionSizing(config)
            
            # Test fixed sizing
            result = position_sizing._calculate_fixed_size(18000, 100)
            
            assert result.size > 0, "Position size should be positive"
            assert result.risk_amount > 0, "Risk amount should be positive"
            assert result.method_used == "fixed", "Method should be fixed"
            
            # Test Kelly sizing (with insufficient data)
            kelly_result = await position_sizing._calculate_kelly_size("NIFTY", "test_strategy", 18000, 100)
            assert kelly_result >= 0, "Kelly size should be non-negative"
            
            # Test volatility sizing (with insufficient data)
            vol_result = await position_sizing._calculate_volatility_size("NIFTY", 18000, 100)
            assert vol_result >= 0, "Volatility size should be non-negative"
            
            return {
                'status': 'PASSED',
                'tests_run': 3,
                'tests_passed': 3,
                'details': {
                    'fixed_sizing': 'PASSED',
                    'kelly_sizing': 'PASSED',
                    'volatility_sizing': 'PASSED'
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Position sizing test failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'tests_run': 3,
                'tests_passed': 0
            }
    
    async def _test_circuit_breakers(self) -> Dict[str, Any]:
        """Test circuit breaker system"""
        try:
            # Create circuit breaker system
            config = {}
            circuit_breaker = CircuitBreakerSystem(config)
            
            # Test initialization
            assert len(circuit_breaker.circuit_breakers) > 0, "Should have circuit breakers"
            assert len(circuit_breaker.emergency_stops) > 0, "Should have emergency stops"
            
            # Test manual kill switch
            await circuit_breaker.manual_kill_switch("Test kill switch")
            
            assert circuit_breaker.emergency_mode == True, "Should be in emergency mode"
            assert circuit_breaker.trading_enabled == False, "Trading should be disabled"
            
            # Test system status
            status = circuit_breaker.get_system_status()
            
            assert 'system_enabled' in status, "Status should include system_enabled"
            assert 'trading_enabled' in status, "Status should include trading_enabled"
            assert 'emergency_mode' in status, "Status should include emergency_mode"
            
            return {
                'status': 'PASSED',
                'tests_run': 3,
                'tests_passed': 3,
                'details': {
                    'initialization': 'PASSED',
                    'kill_switch': 'PASSED',
                    'status_reporting': 'PASSED'
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Circuit breaker test failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'tests_run': 3,
                'tests_passed': 0
            }
    
    async def _test_health_monitor(self) -> Dict[str, Any]:
        """Test agent health monitor"""
        try:
            # Mock event bus
            mock_event_bus = AsyncMock()
            mock_config = Mock()
            
            # Create health monitor
            health_monitor = AgentHealthMonitor(mock_event_bus, mock_config, "test_session")
            
            # Test initialization
            assert health_monitor.name == "AgentHealthMonitor"
            assert health_monitor.auto_restart_enabled == True
            
            # Test health summary
            summary = health_monitor.get_system_health_summary()
            
            assert 'timestamp' in summary, "Summary should include timestamp"
            assert 'overall_health' in summary, "Summary should include overall_health"
            assert 'total_agents' in summary, "Summary should include total_agents"
            
            return {
                'status': 'PASSED',
                'tests_run': 2,
                'tests_passed': 2,
                'details': {
                    'initialization': 'PASSED',
                    'health_summary': 'PASSED'
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Health monitor test failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'tests_run': 2,
                'tests_passed': 0
            }
    
    async def _run_integration_tests(self) -> Dict[str, Any]:
        """Test system integration"""
        try:
            results = {}
            
            # Test end-to-end trade flow
            results['trade_flow'] = await self._test_trade_flow_integration()
            
            # Test risk management integration
            results['risk_integration'] = await self._test_risk_integration()
            
            # Test monitoring integration
            results['monitoring_integration'] = await self._test_monitoring_integration()
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Integration tests failed: {e}")
            return {'error': str(e)}
    
    async def _run_performance_tests(self) -> Dict[str, Any]:
        """Test system performance"""
        try:
            results = {}
            
            # Test execution latency
            results['latency'] = await self._test_execution_latency()
            
            # Test throughput
            results['throughput'] = await self._test_system_throughput()
            
            # Test memory usage
            results['memory'] = await self._test_memory_usage()
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Performance tests failed: {e}")
            return {'error': str(e)}
    
    async def _run_risk_tests(self) -> Dict[str, Any]:
        """Test risk management scenarios"""
        try:
            results = {}
            
            for i, scenario in enumerate(self.test_config['risk_test_scenarios']):
                test_name = f"risk_scenario_{i+1}"
                results[test_name] = await self._test_risk_scenario(scenario)
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Risk tests failed: {e}")
            return {'error': str(e)}
    
    async def _run_recovery_tests(self) -> Dict[str, Any]:
        """Test recovery system"""
        try:
            # Create recovery system
            config = {
                'max_recovery_time_days': 30,
                'emergency_drawdown_threshold': 0.15,
                'full_recovery_threshold': 0.02
            }
            
            recovery_system = DrawdownRecoverySystem(config)
            
            # Test recovery initiation
            success = await recovery_system.initiate_recovery(0.12, 88000)  # 12% drawdown
            
            assert success == True, "Recovery should initiate successfully"
            assert recovery_system.recovery_active == True, "Recovery should be active"
            
            # Test recovery status
            status = recovery_system.get_recovery_status()
            
            assert status['recovery_active'] == True, "Status should show recovery active"
            assert 'current_phase' in status, "Status should include current phase"
            
            return {
                'status': 'PASSED',
                'tests_run': 2,
                'tests_passed': 2,
                'details': {
                    'recovery_initiation': 'PASSED',
                    'status_reporting': 'PASSED'
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Recovery tests failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'tests_run': 2,
                'tests_passed': 0
            }
    
    def _calculate_overall_status(self, test_results: Dict[str, Any]) -> str:
        """Calculate overall test status"""
        try:
            failed_tests = []
            
            # Check component tests
            for component, result in test_results.get('component_tests', {}).items():
                if result.get('status') != 'PASSED':
                    failed_tests.append(f"component_{component}")
            
            # Check integration tests
            for test, result in test_results.get('integration_tests', {}).items():
                if result.get('status') != 'PASSED':
                    failed_tests.append(f"integration_{test}")
            
            # Check performance tests
            for test, result in test_results.get('performance_tests', {}).items():
                if result.get('status') != 'PASSED':
                    failed_tests.append(f"performance_{test}")
            
            # Check risk tests
            for test, result in test_results.get('risk_tests', {}).items():
                if result.get('status') != 'PASSED':
                    failed_tests.append(f"risk_{test}")
            
            # Check recovery tests
            if test_results.get('recovery_tests', {}).get('status') != 'PASSED':
                failed_tests.append('recovery_tests')
            
            if len(failed_tests) == 0:
                return 'PASSED'
            elif len(failed_tests) <= 2:
                return 'PASSED_WITH_WARNINGS'
            else:
                return 'FAILED'
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate overall status: {e}")
            return 'FAILED'
    
    def _generate_test_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test summary"""
        try:
            total_tests = 0
            passed_tests = 0
            
            # Count component tests
            for result in test_results.get('component_tests', {}).values():
                if isinstance(result, dict) and 'tests_run' in result:
                    total_tests += result['tests_run']
                    passed_tests += result.get('tests_passed', 0)
            
            # Count other tests
            for category in ['integration_tests', 'performance_tests', 'risk_tests']:
                for result in test_results.get(category, {}).values():
                    if isinstance(result, dict) and 'tests_run' in result:
                        total_tests += result['tests_run']
                        passed_tests += result.get('tests_passed', 0)
            
            # Count recovery tests
            recovery_result = test_results.get('recovery_tests', {})
            if isinstance(recovery_result, dict) and 'tests_run' in recovery_result:
                total_tests += recovery_result['tests_run']
                passed_tests += recovery_result.get('tests_passed', 0)
            
            success_rate = passed_tests / total_tests if total_tests > 0 else 0
            
            return {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': success_rate,
                'production_ready': success_rate >= 0.95 and test_results['overall_status'] in ['PASSED', 'PASSED_WITH_WARNINGS']
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate test summary: {e}")
            return {'error': str(e)}

# Test runner
async def main():
    """Run production system tests"""
    test_suite = ProductionSystemTests()
    results = await test_suite.run_all_tests()
    
    print("\n" + "="*80)
    print("PRODUCTION SYSTEM TEST RESULTS")
    print("="*80)
    print(f"Overall Status: {results.get('overall_status', 'UNKNOWN')}")
    
    summary = results.get('test_summary', {})
    if summary:
        print(f"Total Tests: {summary.get('total_tests', 0)}")
        print(f"Passed: {summary.get('passed_tests', 0)}")
        print(f"Failed: {summary.get('failed_tests', 0)}")
        print(f"Success Rate: {summary.get('success_rate', 0):.1%}")
        print(f"Production Ready: {summary.get('production_ready', False)}")
    
    print("="*80)
    
    # Save results
    with open(f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
