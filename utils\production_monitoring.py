#!/usr/bin/env python3
"""
Production Monitoring and Alerting System
=========================================

Comprehensive monitoring system for production trading with:
- Real-time system health monitoring
- Performance tracking and alerting
- Risk monitoring and notifications
- Trade execution monitoring
- System resource monitoring
- Multi-channel alerting (email, SMS, webhook)

Features:
- Configurable alert thresholds
- Alert escalation and de-duplication
- Performance dashboards
- Health check endpoints
- Automated reporting
- Integration with external monitoring tools
"""

import asyncio
import logging
import json
import time
import psutil
import smtplib
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import aiohttp

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class AlertChannel(Enum):
    """Alert delivery channels"""
    EMAIL = "email"
    SMS = "sms"
    WEBHOOK = "webhook"
    SLACK = "slack"
    TELEGRAM = "telegram"

class MonitoringMetric(Enum):
    """Monitoring metrics"""
    SYSTEM_HEALTH = "system_health"
    TRADING_PERFORMANCE = "trading_performance"
    RISK_METRICS = "risk_metrics"
    EXECUTION_LATENCY = "execution_latency"
    API_HEALTH = "api_health"
    PORTFOLIO_STATUS = "portfolio_status"
    STRATEGY_PERFORMANCE = "strategy_performance"

@dataclass
class Alert:
    """Alert definition"""
    alert_id: str
    severity: AlertSeverity
    metric: MonitoringMetric
    title: str
    message: str
    timestamp: datetime
    channels: List[AlertChannel]
    acknowledged: bool = False
    resolved: bool = False
    escalated: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MonitoringThreshold:
    """Monitoring threshold configuration"""
    metric: MonitoringMetric
    warning_threshold: float
    critical_threshold: float
    emergency_threshold: Optional[float] = None
    comparison: str = "greater_than"  # greater_than, less_than, equals
    window_seconds: int = 300  # 5 minutes
    enabled: bool = True

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    process_count: int
    uptime: float
    
@dataclass
class TradingMetrics:
    """Trading performance metrics"""
    timestamp: datetime
    total_trades: int
    successful_trades: int
    failed_trades: int
    total_pnl: float
    unrealized_pnl: float
    realized_pnl: float
    current_drawdown: float
    max_drawdown: float
    win_rate: float
    avg_execution_time: float
    active_positions: int

class ProductionMonitoring:
    """
    Comprehensive production monitoring system
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Alert management
        self.active_alerts = {}
        self.alert_history = []
        self.alert_handlers = {}
        
        # Monitoring thresholds
        self.thresholds = {}
        self._initialize_default_thresholds()
        
        # Metrics storage
        self.system_metrics_history = []
        self.trading_metrics_history = []
        self.performance_metrics = {}
        
        # Monitoring state
        self.monitoring_active = True
        self.last_health_check = datetime.now()
        
        # Alert configuration
        self.alert_cooldown = 300  # 5 minutes
        self.max_alerts_per_hour = 20
        self.alert_escalation_time = 1800  # 30 minutes
        
        # External integrations
        self.email_config = config.get('email', {})
        self.webhook_config = config.get('webhook', {})
        self.slack_config = config.get('slack', {})
        
        logger.info("[INIT] Production monitoring system initialized")
    
    def _initialize_default_thresholds(self):
        """Initialize default monitoring thresholds"""
        try:
            default_thresholds = [
                MonitoringThreshold(
                    metric=MonitoringMetric.SYSTEM_HEALTH,
                    warning_threshold=80.0,  # 80% CPU/Memory
                    critical_threshold=90.0,
                    emergency_threshold=95.0
                ),
                MonitoringThreshold(
                    metric=MonitoringMetric.TRADING_PERFORMANCE,
                    warning_threshold=0.05,  # 5% drawdown
                    critical_threshold=0.10,  # 10% drawdown
                    emergency_threshold=0.15  # 15% drawdown
                ),
                MonitoringThreshold(
                    metric=MonitoringMetric.EXECUTION_LATENCY,
                    warning_threshold=2000.0,  # 2 seconds
                    critical_threshold=5000.0,  # 5 seconds
                    emergency_threshold=10000.0  # 10 seconds
                ),
                MonitoringThreshold(
                    metric=MonitoringMetric.API_HEALTH,
                    warning_threshold=0.1,  # 10% failure rate
                    critical_threshold=0.25,  # 25% failure rate
                    emergency_threshold=0.5,  # 50% failure rate
                    comparison="greater_than"
                ),
                MonitoringThreshold(
                    metric=MonitoringMetric.PORTFOLIO_STATUS,
                    warning_threshold=0.8,  # 80% capital utilization
                    critical_threshold=0.9,  # 90% capital utilization
                    emergency_threshold=0.95  # 95% capital utilization
                )
            ]
            
            for threshold in default_thresholds:
                self.thresholds[threshold.metric] = threshold
            
            logger.info(f"[INIT] Initialized {len(default_thresholds)} monitoring thresholds")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize thresholds: {e}")
    
    async def start_monitoring(self):
        """Start the monitoring system"""
        try:
            self.monitoring_active = True
            
            # Start monitoring loops
            asyncio.create_task(self._system_monitoring_loop())
            asyncio.create_task(self._trading_monitoring_loop())
            asyncio.create_task(self._alert_processing_loop())
            asyncio.create_task(self._health_check_loop())
            asyncio.create_task(self._reporting_loop())
            
            logger.info("[START] Production monitoring started")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop the monitoring system"""
        self.monitoring_active = False
        logger.info("[STOP] Production monitoring stopped")
    
    async def _system_monitoring_loop(self):
        """Monitor system resources"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                metrics = await self._collect_system_metrics()
                self.system_metrics_history.append(metrics)
                
                # Keep only recent history
                if len(self.system_metrics_history) > 1440:  # 24 hours at 1-minute intervals
                    self.system_metrics_history = self.system_metrics_history[-1440:]
                
                # Check thresholds
                await self._check_system_thresholds(metrics)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"[ERROR] System monitoring failed: {e}")
                await asyncio.sleep(30)
    
    async def _trading_monitoring_loop(self):
        """Monitor trading performance"""
        while self.monitoring_active:
            try:
                # Collect trading metrics
                metrics = await self._collect_trading_metrics()
                self.trading_metrics_history.append(metrics)
                
                # Keep only recent history
                if len(self.trading_metrics_history) > 1440:
                    self.trading_metrics_history = self.trading_metrics_history[-1440:]
                
                # Check thresholds
                await self._check_trading_thresholds(metrics)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"[ERROR] Trading monitoring failed: {e}")
                await asyncio.sleep(30)
    
    async def _alert_processing_loop(self):
        """Process and deliver alerts"""
        while self.monitoring_active:
            try:
                await self._process_pending_alerts()
                await self._check_alert_escalation()
                await self._cleanup_resolved_alerts()
                
                await asyncio.sleep(30)  # Process every 30 seconds
                
            except Exception as e:
                logger.error(f"[ERROR] Alert processing failed: {e}")
                await asyncio.sleep(10)
    
    async def _health_check_loop(self):
        """Perform comprehensive health checks"""
        while self.monitoring_active:
            try:
                health_status = await self._perform_health_check()
                
                if not health_status['healthy']:
                    await self._create_alert(
                        AlertSeverity.CRITICAL,
                        MonitoringMetric.SYSTEM_HEALTH,
                        "System Health Check Failed",
                        f"Health check failed: {health_status['issues']}",
                        [AlertChannel.EMAIL, AlertChannel.WEBHOOK]
                    )
                
                self.last_health_check = datetime.now()
                await asyncio.sleep(300)  # Health check every 5 minutes
                
            except Exception as e:
                logger.error(f"[ERROR] Health check failed: {e}")
                await asyncio.sleep(60)
    
    async def _reporting_loop(self):
        """Generate periodic reports"""
        while self.monitoring_active:
            try:
                # Generate hourly summary
                if datetime.now().minute == 0:
                    await self._generate_hourly_report()
                
                # Generate daily summary
                if datetime.now().hour == 0 and datetime.now().minute == 0:
                    await self._generate_daily_report()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"[ERROR] Reporting failed: {e}")
                await asyncio.sleep(300)
    
    async def _collect_system_metrics(self) -> SystemMetrics:
        """Collect system performance metrics"""
        try:
            # CPU and memory usage
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network I/O
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # Process information
            process_count = len(psutil.pids())
            
            # System uptime
            boot_time = psutil.boot_time()
            uptime = time.time() - boot_time
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                network_io=network_io,
                process_count=process_count,
                uptime=uptime
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to collect system metrics: {e}")
            return None
    
    async def _collect_trading_metrics(self) -> TradingMetrics:
        """Collect trading performance metrics"""
        try:
            # This would integrate with your trading system
            # For now, return placeholder metrics
            
            return TradingMetrics(
                timestamp=datetime.now(),
                total_trades=0,
                successful_trades=0,
                failed_trades=0,
                total_pnl=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                current_drawdown=0.0,
                max_drawdown=0.0,
                win_rate=0.0,
                avg_execution_time=0.0,
                active_positions=0
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to collect trading metrics: {e}")
            return None
    
    async def _create_alert(
        self,
        severity: AlertSeverity,
        metric: MonitoringMetric,
        title: str,
        message: str,
        channels: List[AlertChannel],
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Create and queue an alert"""
        try:
            alert_id = f"{metric.value}_{int(time.time())}"
            
            # Check for duplicate alerts (de-duplication)
            if self._is_duplicate_alert(metric, severity, message):
                logger.debug(f"[ALERT] Duplicate alert suppressed: {title}")
                return
            
            alert = Alert(
                alert_id=alert_id,
                severity=severity,
                metric=metric,
                title=title,
                message=message,
                timestamp=datetime.now(),
                channels=channels,
                metadata=metadata or {}
            )
            
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Keep only recent alert history
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]
            
            logger.warning(f"[ALERT] {severity.value.upper()}: {title} - {message}")
            
            # Immediate delivery for emergency alerts
            if severity == AlertSeverity.EMERGENCY:
                await self._deliver_alert(alert)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create alert: {e}")
    
    async def _deliver_alert(self, alert: Alert):
        """Deliver alert through configured channels"""
        try:
            for channel in alert.channels:
                try:
                    if channel == AlertChannel.EMAIL:
                        await self._send_email_alert(alert)
                    elif channel == AlertChannel.WEBHOOK:
                        await self._send_webhook_alert(alert)
                    elif channel == AlertChannel.SLACK:
                        await self._send_slack_alert(alert)
                    # Add more channels as needed
                    
                except Exception as e:
                    logger.error(f"[ERROR] Failed to deliver alert via {channel.value}: {e}")
            
        except Exception as e:
            logger.error(f"[ERROR] Alert delivery failed: {e}")
    
    async def _send_email_alert(self, alert: Alert):
        """Send alert via email"""
        try:
            if not self.email_config.get('enabled', False):
                return
            
            smtp_server = self.email_config.get('smtp_server')
            smtp_port = self.email_config.get('smtp_port', 587)
            username = self.email_config.get('username')
            password = self.email_config.get('password')
            to_addresses = self.email_config.get('to_addresses', [])
            
            if not all([smtp_server, username, password, to_addresses]):
                logger.warning("[EMAIL] Email configuration incomplete")
                return
            
            # Create message
            msg = MimeMultipart()
            msg['From'] = username
            msg['To'] = ', '.join(to_addresses)
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.title}"
            
            body = f"""
Trading System Alert

Severity: {alert.severity.value.upper()}
Metric: {alert.metric.value}
Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

Message:
{alert.message}

Alert ID: {alert.alert_id}
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(username, password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"[EMAIL] Alert sent: {alert.alert_id}")
            
        except Exception as e:
            logger.error(f"[ERROR] Email alert failed: {e}")
    
    async def _send_webhook_alert(self, alert: Alert):
        """Send alert via webhook"""
        try:
            if not self.webhook_config.get('enabled', False):
                return
            
            webhook_url = self.webhook_config.get('url')
            if not webhook_url:
                return
            
            payload = {
                'alert_id': alert.alert_id,
                'severity': alert.severity.value,
                'metric': alert.metric.value,
                'title': alert.title,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'metadata': alert.metadata
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"[WEBHOOK] Alert sent: {alert.alert_id}")
                    else:
                        logger.error(f"[WEBHOOK] Failed to send alert: {response.status}")
            
        except Exception as e:
            logger.error(f"[ERROR] Webhook alert failed: {e}")
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive monitoring dashboard data"""
        try:
            # Recent system metrics
            recent_system = self.system_metrics_history[-1] if self.system_metrics_history else None
            recent_trading = self.trading_metrics_history[-1] if self.trading_metrics_history else None
            
            # Active alerts summary
            alert_summary = {
                'total': len(self.active_alerts),
                'by_severity': {
                    'emergency': len([a for a in self.active_alerts.values() if a.severity == AlertSeverity.EMERGENCY]),
                    'critical': len([a for a in self.active_alerts.values() if a.severity == AlertSeverity.CRITICAL]),
                    'warning': len([a for a in self.active_alerts.values() if a.severity == AlertSeverity.WARNING]),
                    'info': len([a for a in self.active_alerts.values() if a.severity == AlertSeverity.INFO])
                }
            }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'monitoring_active': self.monitoring_active,
                'last_health_check': self.last_health_check.isoformat(),
                'system_metrics': {
                    'cpu_usage': recent_system.cpu_usage if recent_system else 0,
                    'memory_usage': recent_system.memory_usage if recent_system else 0,
                    'disk_usage': recent_system.disk_usage if recent_system else 0,
                    'uptime': recent_system.uptime if recent_system else 0
                },
                'trading_metrics': {
                    'total_trades': recent_trading.total_trades if recent_trading else 0,
                    'win_rate': recent_trading.win_rate if recent_trading else 0,
                    'current_drawdown': recent_trading.current_drawdown if recent_trading else 0,
                    'total_pnl': recent_trading.total_pnl if recent_trading else 0,
                    'active_positions': recent_trading.active_positions if recent_trading else 0
                },
                'alerts': alert_summary,
                'thresholds': {
                    metric.value: {
                        'warning': threshold.warning_threshold,
                        'critical': threshold.critical_threshold,
                        'emergency': threshold.emergency_threshold,
                        'enabled': threshold.enabled
                    }
                    for metric, threshold in self.thresholds.items()
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate dashboard: {e}")
            return {'error': str(e)}
